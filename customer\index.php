<?php
/**
 * Customer Shop
 * E-Commerce Platform
 */

$pageTitle = 'Shop';
$bodyClass = 'shop-page';

include '../includes/header.php';

// Get filters
$categoryId = $_GET['category'] ?? null;
$search = $_GET['search'] ?? '';
$sortBy = $_GET['sort'] ?? 'created_at';
$sortOrder = $_GET['order'] ?? 'DESC';
$page = max(1, intval($_GET['page'] ?? 1));
$itemsPerPage = 12;
$offset = ($page - 1) * $itemsPerPage;

// Build filters
$filters = ['status' => 'active'];
if ($categoryId) {
    $filters['category_id'] = $categoryId;
}
if ($search) {
    $filters['search'] = $search;
}

// Get products
$products = getProducts($filters, $itemsPerPage, $offset);

// Get total count for pagination
$totalProducts = $database->fetch(
    "SELECT COUNT(*) as count FROM products p WHERE p.status = 'active'" . 
    ($categoryId ? " AND p.category_id = ?" : "") .
    ($search ? " AND (p.name LIKE ? OR p.description LIKE ?)" : ""),
    array_filter([
        $categoryId,
        $search ? "%$search%" : null,
        $search ? "%$search%" : null
    ])
)['count'];

$totalPages = ceil($totalProducts / $itemsPerPage);

// Get categories for filter
$categories = getCategoryTree();
?>

<div class="container">
    <!-- Shop Header -->
    <div class="shop-header">
        <h1>Shop</h1>
        <p>Discover amazing products from trusted sellers</p>
    </div>
    
    <!-- Filters and Search -->
    <div class="shop-filters">
        <div class="row">
            <div class="col-3">
                <div class="filter-sidebar">
                    <div class="filter-section">
                        <h3>Categories</h3>
                        <ul class="category-list">
                            <li>
                                <a href="index.php" class="<?php echo !$categoryId ? 'active' : ''; ?>">
                                    All Categories
                                </a>
                            </li>
                            <?php foreach ($categories as $category): ?>
                            <li>
                                <a href="index.php?category=<?php echo $category['id']; ?>" 
                                   class="<?php echo $categoryId == $category['id'] ? 'active' : ''; ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </a>
                                <?php if (!empty($category['children'])): ?>
                                <ul class="subcategory-list">
                                    <?php foreach ($category['children'] as $child): ?>
                                    <li>
                                        <a href="index.php?category=<?php echo $child['id']; ?>"
                                           class="<?php echo $categoryId == $child['id'] ? 'active' : ''; ?>">
                                            <?php echo htmlspecialchars($child['name']); ?>
                                        </a>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                                <?php endif; ?>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-9">
                <!-- Search and Sort -->
                <div class="shop-controls">
                    <div class="search-section">
                        <form method="GET" class="search-form">
                            <?php if ($categoryId): ?>
                            <input type="hidden" name="category" value="<?php echo $categoryId; ?>">
                            <?php endif; ?>
                            <div class="search-box">
                                <input type="text" 
                                       name="search" 
                                       placeholder="Search products..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                                <button type="submit" class="btn btn-primary">Search</button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="sort-section">
                        <form method="GET" class="sort-form">
                            <?php if ($categoryId): ?>
                            <input type="hidden" name="category" value="<?php echo $categoryId; ?>">
                            <?php endif; ?>
                            <?php if ($search): ?>
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                            <?php endif; ?>
                            <select name="sort" onchange="this.form.submit()">
                                <option value="created_at" <?php echo $sortBy === 'created_at' ? 'selected' : ''; ?>>Newest First</option>
                                <option value="name" <?php echo $sortBy === 'name' ? 'selected' : ''; ?>>Name A-Z</option>
                                <option value="price" <?php echo $sortBy === 'price' ? 'selected' : ''; ?>>Price Low to High</option>
                                <option value="price_desc" <?php echo $sortBy === 'price_desc' ? 'selected' : ''; ?>>Price High to Low</option>
                                <option value="featured" <?php echo $sortBy === 'featured' ? 'selected' : ''; ?>>Featured</option>
                            </select>
                        </form>
                    </div>
                </div>
                
                <!-- Results Info -->
                <div class="results-info">
                    <p>Showing <?php echo count($products); ?> of <?php echo $totalProducts; ?> products
                    <?php if ($search): ?>
                        for "<?php echo htmlspecialchars($search); ?>"
                    <?php endif; ?>
                    <?php if ($categoryId): ?>
                        <?php 
                        $currentCategory = $database->fetch("SELECT name FROM categories WHERE id = ?", [$categoryId]);
                        if ($currentCategory): ?>
                            in <?php echo htmlspecialchars($currentCategory['name']); ?>
                        <?php endif; ?>
                    <?php endif; ?>
                    </p>
                </div>
                
                <!-- Products Grid -->
                <?php if (empty($products)): ?>
                <div class="no-products">
                    <h3>No products found</h3>
                    <p>Try adjusting your search criteria or browse different categories.</p>
                    <a href="index.php" class="btn btn-primary">View All Products</a>
                </div>
                <?php else: ?>
                <div class="products-grid">
                    <?php foreach ($products as $product): ?>
                    <div class="product-card">
                        <a href="product.php?id=<?php echo $product['id']; ?>">
                            <div class="product-image">
                                <?php 
                                $images = json_decode($product['images'], true);
                                $firstImage = !empty($images) ? $images[0] : null;
                                ?>
                                <?php if ($firstImage): ?>
                                <img src="../assets/uploads/<?php echo $firstImage; ?>" 
                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     onerror="this.parentElement.innerHTML='<div class=\'product-placeholder\'>📦</div>'">
                                <?php else: ?>
                                <div class="product-placeholder">📦</div>
                                <?php endif; ?>
                                
                                <?php if ($product['featured']): ?>
                                <span class="product-badge featured">Featured</span>
                                <?php endif; ?>
                                
                                <?php if ($product['sale_price']): ?>
                                <span class="product-badge sale">Sale</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="product-info">
                                <h3 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h3>
                                <p class="product-description"><?php echo htmlspecialchars(substr($product['short_description'], 0, 100)); ?>...</p>
                                
                                <div class="product-price">
                                    <?php if ($product['sale_price']): ?>
                                    <span class="sale-price"><?php echo formatCurrency($product['sale_price']); ?></span>
                                    <span class="original-price"><?php echo formatCurrency($product['price']); ?></span>
                                    <?php else: ?>
                                    <span class="current-price"><?php echo formatCurrency($product['price']); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <p class="product-seller">by <?php echo htmlspecialchars($product['seller_name']); ?></p>
                                
                                <div class="product-stock">
                                    <?php if ($product['stock_quantity'] > 0): ?>
                                    <span class="in-stock">In Stock (<?php echo $product['stock_quantity']; ?>)</span>
                                    <?php else: ?>
                                    <span class="out-of-stock">Out of Stock</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </a>
                        
                        <div class="product-actions">
                            <?php if ($product['stock_quantity'] > 0): ?>
                                <?php if ($currentUser && $currentUser['role'] === 'customer'): ?>
                                <button type="button" 
                                        class="btn btn-primary btn-full"
                                        onclick="addToCart(<?php echo $product['id']; ?>)">
                                    Add to Cart
                                </button>
                                <?php else: ?>
                                <a href="../auth/login.php" class="btn btn-outline btn-full">Login to Buy</a>
                                <?php endif; ?>
                            <?php else: ?>
                            <button type="button" class="btn btn-secondary btn-full" disabled>
                                Out of Stock
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?><?php echo $categoryId ? '&category=' . $categoryId : ''; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">&laquo; Previous</a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <?php if ($i === $page): ?>
                    <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                    <a href="?page=<?php echo $i; ?><?php echo $categoryId ? '&category=' . $categoryId : ''; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>"><?php echo $i; ?></a>
                    <?php endif; ?>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                    <a href="?page=<?php echo $page + 1; ?><?php echo $categoryId ? '&category=' . $categoryId : ''; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">Next &raquo;</a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.shop-header {
    text-align: center;
    margin: 2rem 0;
}

.shop-filters {
    margin-bottom: 2rem;
}

.filter-sidebar {
    background: #fff;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-section h3 {
    margin-bottom: 1rem;
    color: #333;
}

.category-list {
    list-style: none;
    padding: 0;
}

.category-list li {
    margin-bottom: 0.5rem;
}

.category-list a {
    display: block;
    padding: 0.5rem;
    color: #666;
    text-decoration: none;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

.category-list a:hover,
.category-list a.active {
    background-color: #007bff;
    color: #fff;
}

.subcategory-list {
    margin-left: 1rem;
    margin-top: 0.5rem;
}

.shop-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    gap: 1rem;
}

.search-form {
    flex: 1;
}

.search-box {
    display: flex;
    gap: 0.5rem;
}

.search-box input {
    flex: 1;
}

.results-info {
    margin-bottom: 1rem;
    color: #666;
}

.no-products {
    text-align: center;
    padding: 3rem;
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.product-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 0.25rem;
    text-transform: uppercase;
}

.product-badge.featured {
    background-color: #28a745;
    color: #fff;
}

.product-badge.sale {
    background-color: #dc3545;
    color: #fff;
}

.product-description {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.product-stock {
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.in-stock {
    color: #28a745;
}

.out-of-stock {
    color: #dc3545;
}

.product-actions {
    padding: 1rem;
    border-top: 1px solid #eee;
}

@media (max-width: 768px) {
    .shop-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .row {
        flex-direction: column;
    }
    
    .filter-sidebar {
        margin-bottom: 2rem;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
