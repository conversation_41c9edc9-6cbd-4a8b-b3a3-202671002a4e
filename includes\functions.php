<?php
/**
 * Common Functions
 * E-Commerce Platform
 */

// Prevent direct access
if (!defined('APP_NAME')) {
    die('Direct access not permitted');
}

/**
 * User Management Functions
 */
function createUser($userData) {
    global $database;
    
    // Hash password
    $userData['password'] = password_hash($userData['password'], HASH_ALGO);
    
    try {
        return $database->insert('users', $userData);
    } catch (Exception $e) {
        handleError("Failed to create user: " . $e->getMessage());
        return false;
    }
}

function authenticateUser($email, $password) {
    global $database;
    
    $user = $database->fetch(
        "SELECT * FROM users WHERE email = ? AND status = 'active'",
        [$email]
    );
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        return $user;
    }
    
    return false;
}

function logoutUser() {
    session_destroy();
    session_start();
}

/**
 * Product Management Functions
 */
function getProducts($filters = [], $limit = null, $offset = 0) {
    global $database;
    
    $sql = "SELECT p.*, c.name as category_name, u.username as seller_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            LEFT JOIN users u ON p.seller_id = u.id 
            WHERE 1=1";
    
    $params = [];
    
    if (isset($filters['status'])) {
        $sql .= " AND p.status = ?";
        $params[] = $filters['status'];
    }
    
    if (isset($filters['category_id'])) {
        $sql .= " AND p.category_id = ?";
        $params[] = $filters['category_id'];
    }
    
    if (isset($filters['seller_id'])) {
        $sql .= " AND p.seller_id = ?";
        $params[] = $filters['seller_id'];
    }
    
    if (isset($filters['search'])) {
        $sql .= " AND (p.name LIKE ? OR p.description LIKE ?)";
        $searchTerm = '%' . $filters['search'] . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $sql .= " ORDER BY p.created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
    }
    
    return $database->fetchAll($sql, $params);
}

function getProduct($id) {
    global $database;
    
    return $database->fetch(
        "SELECT p.*, c.name as category_name, u.username as seller_name, u.first_name, u.last_name
         FROM products p 
         LEFT JOIN categories c ON p.category_id = c.id 
         LEFT JOIN users u ON p.seller_id = u.id 
         WHERE p.id = ?",
        [$id]
    );
}

/**
 * Category Management Functions
 */
function getCategories($parent_id = null) {
    global $database;
    
    $sql = "SELECT * FROM categories WHERE status = 'active'";
    $params = [];
    
    if ($parent_id === null) {
        $sql .= " AND parent_id IS NULL";
    } else {
        $sql .= " AND parent_id = ?";
        $params[] = $parent_id;
    }
    
    $sql .= " ORDER BY sort_order, name";
    
    return $database->fetchAll($sql, $params);
}

function getCategoryTree() {
    $categories = getCategories();
    $tree = [];
    
    foreach ($categories as $category) {
        $category['children'] = getCategories($category['id']);
        $tree[] = $category;
    }
    
    return $tree;
}

/**
 * Order Management Functions
 */
function createOrder($orderData) {
    global $database;
    
    try {
        $database->beginTransaction();
        
        // Generate order number
        $orderData['order_number'] = generateOrderNumber();
        
        // Create order
        $orderId = $database->insert('orders', $orderData);
        
        $database->commit();
        return $orderId;
    } catch (Exception $e) {
        $database->rollback();
        handleError("Failed to create order: " . $e->getMessage());
        return false;
    }
}

function addOrderItem($orderItemData) {
    global $database;
    
    return $database->insert('order_items', $orderItemData);
}

function getOrders($filters = [], $limit = null, $offset = 0) {
    global $database;
    
    $sql = "SELECT o.*, u.first_name, u.last_name, u.email 
            FROM orders o 
            LEFT JOIN users u ON o.customer_id = u.id 
            WHERE 1=1";
    
    $params = [];
    
    if (isset($filters['customer_id'])) {
        $sql .= " AND o.customer_id = ?";
        $params[] = $filters['customer_id'];
    }
    
    if (isset($filters['status'])) {
        $sql .= " AND o.status = ?";
        $params[] = $filters['status'];
    }
    
    $sql .= " ORDER BY o.created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
    }
    
    return $database->fetchAll($sql, $params);
}

/**
 * Cart Management Functions
 */
function addToCart($customerId, $productId, $quantity = 1) {
    global $database;
    
    // Check if item already exists in cart
    $existingItem = $database->fetch(
        "SELECT * FROM cart_items WHERE customer_id = ? AND product_id = ?",
        [$customerId, $productId]
    );
    
    if ($existingItem) {
        // Update quantity
        return $database->update(
            'cart_items',
            ['quantity' => $existingItem['quantity'] + $quantity],
            'id = ?',
            [$existingItem['id']]
        );
    } else {
        // Add new item
        return $database->insert('cart_items', [
            'customer_id' => $customerId,
            'product_id' => $productId,
            'quantity' => $quantity
        ]);
    }
}

function getCartItems($customerId) {
    global $database;
    
    return $database->fetchAll(
        "SELECT ci.*, p.name, p.price, p.images, p.stock_quantity 
         FROM cart_items ci 
         LEFT JOIN products p ON ci.product_id = p.id 
         WHERE ci.customer_id = ? AND p.status = 'active'
         ORDER BY ci.created_at DESC",
        [$customerId]
    );
}

function removeFromCart($customerId, $productId) {
    global $database;
    
    return $database->delete(
        'cart_items',
        'customer_id = ? AND product_id = ?',
        [$customerId, $productId]
    );
}

function clearCart($customerId) {
    global $database;
    
    return $database->delete(
        'cart_items',
        'customer_id = ?',
        [$customerId]
    );
}

/**
 * Subscription Management Functions
 */
function getSubscriptionPlans() {
    global $database;
    
    return $database->fetchAll(
        "SELECT * FROM subscription_plans WHERE status = 'active' ORDER BY price ASC"
    );
}

function getSellerSubscription($sellerId) {
    global $database;
    
    return $database->fetch(
        "SELECT ss.*, sp.name as plan_name, sp.commission_rate, sp.max_products 
         FROM seller_subscriptions ss 
         LEFT JOIN subscription_plans sp ON ss.plan_id = sp.id 
         WHERE ss.seller_id = ? AND ss.status = 'active' 
         ORDER BY ss.end_date DESC LIMIT 1",
        [$sellerId]
    );
}

/**
 * Commission Calculation Functions
 */
function calculateCommission($amount, $sellerId) {
    $subscription = getSellerSubscription($sellerId);
    
    if (!$subscription) {
        return 0; // No active subscription
    }
    
    $commissionRate = $subscription['commission_rate'] / 100;
    return $amount * $commissionRate;
}

/**
 * Statistics Functions
 */
function getDashboardStats($role = 'admin', $userId = null) {
    global $database;
    
    $stats = [];
    
    if ($role === 'admin') {
        $stats['total_users'] = $database->fetch("SELECT COUNT(*) as count FROM users")['count'];
        $stats['total_products'] = $database->fetch("SELECT COUNT(*) as count FROM products")['count'];
        $stats['total_orders'] = $database->fetch("SELECT COUNT(*) as count FROM orders")['count'];
        $stats['total_revenue'] = $database->fetch("SELECT SUM(total) as total FROM orders WHERE payment_status = 'paid'")['total'] ?? 0;
    } elseif ($role === 'seller' && $userId) {
        $stats['total_products'] = $database->fetch("SELECT COUNT(*) as count FROM products WHERE seller_id = ?", [$userId])['count'];
        $stats['total_orders'] = $database->fetch("SELECT COUNT(*) as count FROM order_items oi LEFT JOIN orders o ON oi.order_id = o.id WHERE oi.seller_id = ?", [$userId])['count'];
        $stats['total_revenue'] = $database->fetch("SELECT SUM(oi.total) as total FROM order_items oi LEFT JOIN orders o ON oi.order_id = o.id WHERE oi.seller_id = ? AND o.payment_status = 'paid'", [$userId])['total'] ?? 0;
        $stats['pending_payouts'] = $database->fetch("SELECT SUM(amount) as total FROM payouts WHERE seller_id = ? AND status = 'pending'", [$userId])['total'] ?? 0;
    }
    
    return $stats;
}
?>
