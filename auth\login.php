<?php
/**
 * User Login
 * E-Commerce Platform
 */

$pageTitle = 'Login';
$bodyClass = 'auth-page';

include '../includes/header.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    switch ($user['role']) {
        case 'admin':
            redirect('../admin/index.php');
            break;
        case 'seller':
            redirect('../seller/index.php');
            break;
        default:
            redirect('../customer/index.php');
    }
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields';
    } else {
        $user = authenticateUser($email, $password);
        
        if ($user) {
            // Set remember me cookie if requested
            if ($remember) {
                setcookie('remember_token', base64_encode($user['id'] . ':' . $user['email']), time() + (30 * 24 * 60 * 60), '/');
            }
            
            showAlert('Welcome back, ' . $user['first_name'] . '!', 'success');
            
            // Redirect based on role
            switch ($user['role']) {
                case 'admin':
                    redirect('../admin/index.php');
                    break;
                case 'seller':
                    redirect('../seller/index.php');
                    break;
                default:
                    redirect('../customer/index.php');
            }
        } else {
            $error = 'Invalid email or password';
        }
    }
}
?>

<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <h1>Welcome Back</h1>
            <p>Sign in to your account</p>
        </div>
        
        <?php if ($error): ?>
        <div class="alert alert-error">
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <form method="POST" class="auth-form">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                    required 
                    autocomplete="email"
                    placeholder="Enter your email"
                >
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    required 
                    autocomplete="current-password"
                    placeholder="Enter your password"
                >
            </div>
            
            <div class="form-group form-row">
                <label class="checkbox-label">
                    <input type="checkbox" name="remember" value="1">
                    <span class="checkmark"></span>
                    Remember me
                </label>
                <a href="forgot-password.php" class="forgot-link">Forgot password?</a>
            </div>
            
            <button type="submit" class="btn btn-primary btn-full">Sign In</button>
        </form>
        
        <div class="auth-footer">
            <p>Don't have an account? <a href="register.php">Sign up here</a></p>
        </div>
        
        <div class="demo-accounts">
            <h3>Demo Accounts</h3>
            <div class="demo-grid">
                <div class="demo-account">
                    <h4>Admin</h4>
                    <p>Email: <EMAIL></p>
                    <p>Password: admin123</p>
                    <button type="button" class="btn btn-outline btn-small" onclick="fillDemo('<EMAIL>', 'admin123')">Use Demo</button>
                </div>
                <div class="demo-account">
                    <h4>Seller</h4>
                    <p>Email: <EMAIL></p>
                    <p>Password: seller123</p>
                    <button type="button" class="btn btn-outline btn-small" onclick="fillDemo('<EMAIL>', 'seller123')">Use Demo</button>
                </div>
                <div class="demo-account">
                    <h4>Customer</h4>
                    <p>Email: <EMAIL></p>
                    <p>Password: customer123</p>
                    <button type="button" class="btn btn-outline btn-small" onclick="fillDemo('<EMAIL>', 'customer123')">Use Demo</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function fillDemo(email, password) {
    document.getElementById('email').value = email;
    document.getElementById('password').value = password;
}
</script>

<?php include '../includes/footer.php'; ?>
