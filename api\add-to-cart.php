<?php
/**
 * Add to Cart API
 * Adds a product to the user's cart
 */

header('Content-Type: application/json');
require_once '../config/config.php';

// Check if user is logged in and is a customer
if (!isLoggedIn() || !isCustomer()) {
    echo json_encode([
        'success' => false,
        'message' => 'Please log in to add items to cart'
    ]);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
    exit;
}

try {
    $customerId = $_SESSION['user_id'];
    $productId = intval($_POST['product_id'] ?? 0);
    $quantity = intval($_POST['quantity'] ?? 1);
    
    if (!$productId || $quantity <= 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid product or quantity'
        ]);
        exit;
    }
    
    // Check if product exists and is active
    $product = $database->fetch(
        "SELECT * FROM products WHERE id = ? AND status = 'active'",
        [$productId]
    );
    
    if (!$product) {
        echo json_encode([
            'success' => false,
            'message' => 'Product not found or unavailable'
        ]);
        exit;
    }
    
    // Check stock availability
    if ($product['stock_quantity'] < $quantity) {
        echo json_encode([
            'success' => false,
            'message' => 'Insufficient stock available'
        ]);
        exit;
    }
    
    // Add to cart
    $result = addToCart($customerId, $productId, $quantity);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Product added to cart successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to add product to cart'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while adding to cart'
    ]);
}
?>
