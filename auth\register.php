<?php
/**
 * User Registration
 * E-Commerce Platform
 */

$pageTitle = 'Register';
$bodyClass = 'auth-page';

include '../includes/header.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('../index.php');
}

$error = '';
$success = '';
$userType = $_GET['type'] ?? 'customer';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $firstName = sanitizeInput($_POST['first_name'] ?? '');
    $lastName = sanitizeInput($_POST['last_name'] ?? '');
    $username = sanitizeInput($_POST['username'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $role = sanitizeInput($_POST['role'] ?? 'customer');
    $terms = isset($_POST['terms']);
    
    // Validation
    if (empty($firstName) || empty($lastName) || empty($username) || empty($email) || empty($password)) {
        $error = 'Please fill in all required fields';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match';
    } elseif (!$terms) {
        $error = 'Please accept the terms and conditions';
    } else {
        // Check if username or email already exists
        $existingUser = $database->fetch(
            "SELECT id FROM users WHERE username = ? OR email = ?",
            [$username, $email]
        );
        
        if ($existingUser) {
            $error = 'Username or email already exists';
        } else {
            // Create user
            $userData = [
                'first_name' => $firstName,
                'last_name' => $lastName,
                'username' => $username,
                'email' => $email,
                'phone' => $phone,
                'password' => $password,
                'role' => in_array($role, ['customer', 'seller']) ? $role : 'customer',
                'status' => 'active'
            ];
            
            $userId = createUser($userData);
            
            if ($userId) {
                $success = 'Account created successfully! You can now log in.';
                
                // Auto-login the user
                $_SESSION['user_id'] = $userId;
                $_SESSION['user_role'] = $userData['role'];
                $_SESSION['user_name'] = $firstName . ' ' . $lastName;
                
                showAlert('Welcome to ' . APP_NAME . ', ' . $firstName . '!', 'success');
                
                // Redirect based on role
                if ($userData['role'] === 'seller') {
                    redirect('../seller/subscription.php');
                } else {
                    redirect('../customer/index.php');
                }
            } else {
                $error = 'Failed to create account. Please try again.';
            }
        }
    }
}
?>

<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <h1>Create Account</h1>
            <p>Join our community of buyers and sellers</p>
        </div>
        
        <?php if ($error): ?>
        <div class="alert alert-error">
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
        <div class="alert alert-success">
            <?php echo htmlspecialchars($success); ?>
        </div>
        <?php endif; ?>
        
        <form method="POST" class="auth-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="first_name">First Name *</label>
                    <input 
                        type="text" 
                        id="first_name" 
                        name="first_name" 
                        value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>"
                        required 
                        placeholder="Enter your first name"
                    >
                </div>
                
                <div class="form-group">
                    <label for="last_name">Last Name *</label>
                    <input 
                        type="text" 
                        id="last_name" 
                        name="last_name" 
                        value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>"
                        required 
                        placeholder="Enter your last name"
                    >
                </div>
            </div>
            
            <div class="form-group">
                <label for="username">Username *</label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                    required 
                    placeholder="Choose a unique username"
                >
            </div>
            
            <div class="form-group">
                <label for="email">Email Address *</label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                    required 
                    placeholder="Enter your email address"
                >
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input 
                    type="tel" 
                    id="phone" 
                    name="phone" 
                    value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                    placeholder="Enter your phone number"
                >
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="password">Password *</label>
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        required 
                        placeholder="Create a password (min. 6 characters)"
                        minlength="6"
                    >
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">Confirm Password *</label>
                    <input 
                        type="password" 
                        id="confirm_password" 
                        name="confirm_password" 
                        required 
                        placeholder="Confirm your password"
                    >
                </div>
            </div>
            
            <div class="form-group">
                <label for="role">Account Type</label>
                <select id="role" name="role">
                    <option value="customer" <?php echo ($userType === 'customer') ? 'selected' : ''; ?>>Customer - Buy products</option>
                    <option value="seller" <?php echo ($userType === 'seller') ? 'selected' : ''; ?>>Seller - Sell products</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="terms" value="1" required>
                    <span class="checkmark"></span>
                    I agree to the <a href="../legal/terms.php" target="_blank">Terms of Service</a> and <a href="../legal/privacy.php" target="_blank">Privacy Policy</a>
                </label>
            </div>
            
            <button type="submit" class="btn btn-primary btn-full">Create Account</button>
        </form>
        
        <div class="auth-footer">
            <p>Already have an account? <a href="login.php">Sign in here</a></p>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});
</script>

<?php include '../includes/footer.php'; ?>
