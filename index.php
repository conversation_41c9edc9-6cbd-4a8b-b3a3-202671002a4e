<?php
/**
 * Homepage
 * E-Commerce Platform
 */

$pageTitle = 'Welcome to ' . APP_NAME;
$pageDescription = 'Discover amazing products from trusted sellers on our e-commerce platform';

include 'includes/header.php';

// Get featured products
$featuredProducts = getProducts(['status' => 'active'], 8);

// Get categories
$categories = getCategories();
?>

<div class="hero-section">
    <div class="hero-container">
        <div class="hero-content">
            <h1 class="hero-title">Welcome to <?php echo APP_NAME; ?></h1>
            <p class="hero-subtitle">Discover amazing products from trusted sellers worldwide</p>
            <div class="hero-actions">
                <a href="customer/index.php" class="btn btn-primary btn-large">Start Shopping</a>
                <?php if (!$currentUser): ?>
                <a href="auth/register.php?type=seller" class="btn btn-secondary btn-large">Become a Seller</a>
                <?php endif; ?>
            </div>
        </div>
        <div class="hero-image">
            <img src="assets/images/hero-image.jpg" alt="E-Commerce Platform" onerror="this.style.display='none'">
        </div>
    </div>
</div>

<section class="features-section">
    <div class="container">
        <h2 class="section-title">Why Choose Us?</h2>
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🛍️</div>
                <h3>Wide Selection</h3>
                <p>Thousands of products from verified sellers across multiple categories</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🚚</div>
                <h3>Fast Shipping</h3>
                <p>Quick and reliable delivery with real-time tracking</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🔒</div>
                <h3>Secure Payments</h3>
                <p>Safe and secure payment processing with buyer protection</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">💰</div>
                <h3>Best Prices</h3>
                <p>Competitive pricing with regular deals and discounts</p>
            </div>
        </div>
    </div>
</section>

<?php if (!empty($categories)): ?>
<section class="categories-section">
    <div class="container">
        <h2 class="section-title">Shop by Category</h2>
        <div class="categories-grid">
            <?php foreach (array_slice($categories, 0, 6) as $category): ?>
            <div class="category-card">
                <a href="customer/index.php?category=<?php echo $category['id']; ?>">
                    <?php if ($category['image']): ?>
                    <img src="assets/uploads/<?php echo $category['image']; ?>" alt="<?php echo htmlspecialchars($category['name']); ?>">
                    <?php else: ?>
                    <div class="category-placeholder">📦</div>
                    <?php endif; ?>
                    <h3><?php echo htmlspecialchars($category['name']); ?></h3>
                    <p><?php echo htmlspecialchars($category['description']); ?></p>
                </a>
            </div>
            <?php endforeach; ?>
        </div>
        <div class="text-center">
            <a href="customer/index.php" class="btn btn-outline">View All Categories</a>
        </div>
    </div>
</section>
<?php endif; ?>

<?php if (!empty($featuredProducts)): ?>
<section class="featured-products-section">
    <div class="container">
        <h2 class="section-title">Featured Products</h2>
        <div class="products-grid">
            <?php foreach ($featuredProducts as $product): ?>
            <div class="product-card">
                <a href="customer/product.php?id=<?php echo $product['id']; ?>">
                    <div class="product-image">
                        <?php 
                        $images = json_decode($product['images'], true);
                        $firstImage = !empty($images) ? $images[0] : null;
                        ?>
                        <?php if ($firstImage): ?>
                        <img src="assets/uploads/<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                        <?php else: ?>
                        <div class="product-placeholder">📦</div>
                        <?php endif; ?>
                    </div>
                    <div class="product-info">
                        <h3 class="product-name"><?php echo htmlspecialchars($product['name']); ?></h3>
                        <p class="product-price">
                            <?php if ($product['sale_price']): ?>
                            <span class="sale-price"><?php echo formatCurrency($product['sale_price']); ?></span>
                            <span class="original-price"><?php echo formatCurrency($product['price']); ?></span>
                            <?php else: ?>
                            <?php echo formatCurrency($product['price']); ?>
                            <?php endif; ?>
                        </p>
                        <p class="product-seller">by <?php echo htmlspecialchars($product['seller_name']); ?></p>
                    </div>
                </a>
            </div>
            <?php endforeach; ?>
        </div>
        <div class="text-center">
            <a href="customer/index.php" class="btn btn-primary">View All Products</a>
        </div>
    </div>
</section>
<?php endif; ?>

<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Start Selling?</h2>
            <p>Join thousands of successful sellers on our platform and grow your business</p>
            <div class="cta-features">
                <div class="cta-feature">
                    <span class="cta-icon">✓</span>
                    <span>Easy product listing</span>
                </div>
                <div class="cta-feature">
                    <span class="cta-icon">✓</span>
                    <span>Flexible commission rates</span>
                </div>
                <div class="cta-feature">
                    <span class="cta-icon">✓</span>
                    <span>Comprehensive analytics</span>
                </div>
                <div class="cta-feature">
                    <span class="cta-icon">✓</span>
                    <span>24/7 support</span>
                </div>
            </div>
            <?php if (!$currentUser): ?>
            <a href="auth/register.php?type=seller" class="btn btn-primary btn-large">Get Started Today</a>
            <?php elseif ($currentUser['role'] === 'customer'): ?>
            <a href="seller/subscription.php" class="btn btn-primary btn-large">Upgrade to Seller</a>
            <?php endif; ?>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
