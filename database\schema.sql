-- E-Commerce Platform Database Schema
-- Created for comprehensive e-commerce system with subscription management

CREATE DATABASE IF NOT EXISTS ecommerce_platform;
USE ecommerce_platform;

-- Users table for all user types (admin, seller, customer)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HAR(50) NOT NULL,
    last_name VA<PERSON>HAR(50) NOT NULL,
    phone VARCHAR(20),
    role ENUM('admin', 'seller', 'customer') NOT NULL DEFAULT 'customer',
    status ENUM('active', 'inactive', 'suspended') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Subscription plans for sellers
CREATE TABLE subscription_plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    commission_rate DECIMAL(5,2) NOT NULL COMMENT 'Commission percentage (e.g., 5.00 for 5%)',
    billing_cycle ENUM('monthly', 'yearly') NOT NULL DEFAULT 'monthly',
    max_products INT DEFAULT NULL COMMENT 'NULL for unlimited',
    features JSON COMMENT 'Additional features as JSON',
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Seller subscriptions
CREATE TABLE seller_subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    seller_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'expired', 'cancelled') NOT NULL DEFAULT 'active',
    auto_renew BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id)
);

-- Product categories
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INT DEFAULT NULL,
    image VARCHAR(255),
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Product policies for admin oversight
CREATE TABLE product_policies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    policy_type ENUM('general', 'category_specific', 'seller_specific') NOT NULL DEFAULT 'general',
    target_id INT DEFAULT NULL COMMENT 'Category ID or Seller ID based on policy_type',
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Products
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    seller_id INT NOT NULL,
    category_id INT NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    price DECIMAL(10,2) NOT NULL,
    sale_price DECIMAL(10,2) DEFAULT NULL,
    sku VARCHAR(100) UNIQUE,
    stock_quantity INT NOT NULL DEFAULT 0,
    manage_stock BOOLEAN DEFAULT TRUE,
    weight DECIMAL(8,2) DEFAULT NULL,
    dimensions VARCHAR(100) DEFAULT NULL,
    images JSON COMMENT 'Array of image URLs',
    status ENUM('active', 'inactive', 'pending_approval', 'rejected') NOT NULL DEFAULT 'pending_approval',
    featured BOOLEAN DEFAULT FALSE,
    meta_title VARCHAR(200),
    meta_description VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id),
    INDEX idx_seller_status (seller_id, status),
    INDEX idx_category_status (category_id, status),
    INDEX idx_featured (featured, status)
);

-- Shipping zones
CREATE TABLE shipping_zones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    countries JSON COMMENT 'Array of country codes',
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Shipping methods
CREATE TABLE shipping_methods (
    id INT PRIMARY KEY AUTO_INCREMENT,
    zone_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    base_rate DECIMAL(10,2) NOT NULL,
    per_kg_rate DECIMAL(10,2) DEFAULT 0,
    free_shipping_threshold DECIMAL(10,2) DEFAULT NULL,
    estimated_delivery_days VARCHAR(20),
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (zone_id) REFERENCES shipping_zones(id) ON DELETE CASCADE
);

-- Customer addresses
CREATE TABLE customer_addresses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    type ENUM('billing', 'shipping') NOT NULL DEFAULT 'shipping',
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    company VARCHAR(100),
    address_line_1 VARCHAR(200) NOT NULL,
    address_line_2 VARCHAR(200),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(2) NOT NULL,
    phone VARCHAR(20),
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Orders
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded') NOT NULL DEFAULT 'pending',
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    shipping_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') NOT NULL DEFAULT 'pending',
    payment_method VARCHAR(50),
    billing_address JSON,
    shipping_address JSON,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(id),
    INDEX idx_customer_status (customer_id, status),
    INDEX idx_order_number (order_number)
);

-- Order items
CREATE TABLE order_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    seller_id INT NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    product_sku VARCHAR(100),
    quantity INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    total DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (seller_id) REFERENCES users(id)
);

-- Transactions for tracking payments and commissions
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    seller_id INT NOT NULL,
    transaction_type ENUM('sale', 'commission', 'refund') NOT NULL DEFAULT 'sale',
    gross_amount DECIMAL(10,2) NOT NULL,
    commission_rate DECIMAL(5,2) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    net_amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'completed', 'failed') NOT NULL DEFAULT 'pending',
    payment_gateway VARCHAR(50),
    gateway_transaction_id VARCHAR(100),
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (seller_id) REFERENCES users(id),
    INDEX idx_seller_status (seller_id, status),
    INDEX idx_order_transaction (order_id)
);

-- Payouts for sellers
CREATE TABLE payouts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    seller_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_date TIMESTAMP NULL,
    processed_by INT NULL COMMENT 'Admin user ID',
    payment_method VARCHAR(50),
    payment_details JSON COMMENT 'Bank details, PayPal email, etc.',
    notes TEXT,
    FOREIGN KEY (seller_id) REFERENCES users(id),
    FOREIGN KEY (processed_by) REFERENCES users(id),
    INDEX idx_seller_status (seller_id, status)
);

-- Shipments for tracking
CREATE TABLE shipments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    tracking_number VARCHAR(100) UNIQUE,
    carrier VARCHAR(100),
    shipping_method_id INT,
    status ENUM('pending', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed') NOT NULL DEFAULT 'pending',
    shipped_date TIMESTAMP NULL,
    estimated_delivery_date DATE NULL,
    delivered_date TIMESTAMP NULL,
    delivery_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (shipping_method_id) REFERENCES shipping_methods(id),
    INDEX idx_tracking (tracking_number),
    INDEX idx_order_status (order_id, status)
);

-- Shopping cart for customers
CREATE TABLE cart_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE KEY unique_customer_product (customer_id, product_id)
);

-- System settings
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') NOT NULL DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert demo data
-- Demo users
INSERT INTO users (username, email, password, first_name, last_name, phone, role, status) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', '+**********', 'admin', 'active'),
('seller1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Seller', '+1234567891', 'seller', 'active'),
('customer1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane', 'Customer', '+1234567892', 'customer', 'active'),
('seller2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mike', 'Johnson', '+1234567893', 'seller', 'active'),
('customer2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sarah', 'Wilson', '+1234567894', 'customer', 'active');

-- Demo subscription plans
INSERT INTO subscription_plans (name, description, price, commission_rate, billing_cycle, max_products, features, status) VALUES
('Basic', 'Perfect for small businesses starting their online journey', 29.99, 8.00, 'monthly', 50, '["Basic analytics", "Email support", "Standard listing"]', 'active'),
('Professional', 'Ideal for growing businesses with advanced features', 79.99, 6.00, 'monthly', 200, '["Advanced analytics", "Priority support", "Featured listings", "Bulk upload"]', 'active'),
('Enterprise', 'Complete solution for large-scale operations', 199.99, 4.00, 'monthly', NULL, '["Premium analytics", "24/7 support", "Premium listings", "API access", "Custom branding"]', 'active'),
('Basic Annual', 'Basic plan with annual billing discount', 299.99, 8.00, 'yearly', 50, '["Basic analytics", "Email support", "Standard listing"]', 'active'),
('Professional Annual', 'Professional plan with annual billing discount', 799.99, 6.00, 'yearly', 200, '["Advanced analytics", "Priority support", "Featured listings", "Bulk upload"]', 'active');

-- Demo seller subscriptions
INSERT INTO seller_subscriptions (seller_id, plan_id, start_date, end_date, status, auto_renew) VALUES
(2, 2, '2024-01-01', '2024-02-01', 'active', TRUE),
(4, 1, '2024-01-15', '2024-02-15', 'active', TRUE);

-- Demo categories
INSERT INTO categories (name, description, parent_id, image, status, sort_order) VALUES
('Electronics', 'Latest electronic devices and gadgets', NULL, NULL, 'active', 1),
('Clothing', 'Fashion and apparel for all ages', NULL, NULL, 'active', 2),
('Home & Garden', 'Everything for your home and garden', NULL, NULL, 'active', 3),
('Sports & Outdoors', 'Sports equipment and outdoor gear', NULL, NULL, 'active', 4),
('Books & Media', 'Books, movies, music and more', NULL, NULL, 'active', 5),
('Smartphones', 'Latest smartphones and accessories', 1, NULL, 'active', 1),
('Laptops', 'Laptops and computer accessories', 1, NULL, 'active', 2),
('Mens Clothing', 'Clothing for men', 2, NULL, 'active', 1),
('Womens Clothing', 'Clothing for women', 2, NULL, 'active', 2),
('Furniture', 'Home furniture and decor', 3, NULL, 'active', 1);

-- Demo product policies
INSERT INTO product_policies (title, description, policy_type, target_id, status) VALUES
('General Product Guidelines', 'All products must be authentic, legal, and in good condition. Prohibited items include counterfeit goods, illegal substances, and hazardous materials.', 'general', NULL, 'active'),
('Electronics Safety Standards', 'Electronic products must meet safety certifications and include proper documentation. Batteries must comply with shipping regulations.', 'category_specific', 1, 'active'),
('Clothing Quality Standards', 'Clothing items must be new or clearly marked as used. All garments must include accurate size charts and material composition.', 'category_specific', 2, 'active'),
('Return Policy Requirements', 'All sellers must accept returns within 30 days for defective items. Return shipping costs are seller responsibility for defective products.', 'general', NULL, 'active');

-- Demo products
INSERT INTO products (seller_id, category_id, name, description, short_description, price, sale_price, sku, stock_quantity, weight, dimensions, images, status, featured, meta_title, meta_description) VALUES
(2, 6, 'iPhone 15 Pro Max', 'Latest iPhone with advanced camera system and A17 Pro chip. Features titanium design, Action Button, and USB-C connectivity.', 'Latest iPhone 15 Pro Max with titanium design', 1199.99, 1099.99, 'IPH15PM-256-TIT', 25, 0.22, '6.3 x 3.02 x 0.32 inches', '["iphone15pro.jpg", "iphone15pro-2.jpg"]', 'active', TRUE, 'iPhone 15 Pro Max - Latest Apple Smartphone', 'Buy the latest iPhone 15 Pro Max with advanced features'),
(2, 7, 'MacBook Pro 16-inch', 'Powerful MacBook Pro with M3 Pro chip, 16-inch Liquid Retina XDR display, and up to 22 hours of battery life.', 'MacBook Pro 16-inch with M3 Pro chip', 2499.99, NULL, 'MBP16-M3PRO-512', 15, 2.1, '14.01 x 9.77 x 0.66 inches', '["macbook-pro-16.jpg"]', 'active', TRUE, 'MacBook Pro 16-inch M3 Pro', 'Professional laptop for creative professionals'),
(4, 8, 'Premium Cotton T-Shirt', 'High-quality 100% organic cotton t-shirt. Comfortable fit with reinforced seams. Available in multiple colors.', 'Premium organic cotton t-shirt', 29.99, 24.99, 'TSHIRT-ORG-M-BLK', 100, 0.2, 'Medium', '["tshirt-black.jpg", "tshirt-white.jpg"]', 'active', FALSE, 'Premium Organic Cotton T-Shirt', 'Comfortable organic cotton t-shirt'),
(4, 9, 'Designer Dress', 'Elegant evening dress perfect for special occasions. Made from premium fabric with attention to detail.', 'Elegant designer evening dress', 149.99, 129.99, 'DRESS-EVE-M-BLU', 20, 0.5, 'Medium', '["dress-blue.jpg"]', 'active', TRUE, 'Designer Evening Dress', 'Elegant dress for special occasions'),
(2, 10, 'Modern Office Chair', 'Ergonomic office chair with lumbar support, adjustable height, and premium materials. Perfect for long work sessions.', 'Ergonomic office chair with lumbar support', 299.99, NULL, 'CHAIR-OFF-ERG-BLK', 30, 15.5, '26 x 26 x 42-46 inches', '["office-chair.jpg"]', 'active', FALSE, 'Ergonomic Office Chair', 'Comfortable office chair for productivity'),
(4, 4, 'Yoga Mat Premium', 'Non-slip yoga mat made from eco-friendly materials. Perfect grip and cushioning for all yoga practices.', 'Premium eco-friendly yoga mat', 49.99, 39.99, 'YOGA-MAT-PREM-PUR', 50, 1.2, '72 x 24 x 0.25 inches', '["yoga-mat.jpg"]', 'active', FALSE, 'Premium Yoga Mat', 'Eco-friendly yoga mat for all practices'),
(2, 5, 'Programming Book Set', 'Complete set of programming books covering JavaScript, Python, and web development. Perfect for beginners and professionals.', 'Complete programming book collection', 89.99, 69.99, 'BOOK-PROG-SET-3', 40, 2.5, '9 x 7 x 3 inches', '["programming-books.jpg"]', 'active', TRUE, 'Programming Book Set', 'Learn programming with this comprehensive book set'),
(4, 6, 'Wireless Earbuds Pro', 'Premium wireless earbuds with active noise cancellation, long battery life, and crystal-clear sound quality.', 'Premium wireless earbuds with ANC', 199.99, 179.99, 'EARBUDS-PRO-WHT', 75, 0.1, '2.5 x 2.5 x 1 inches', '["earbuds-pro.jpg"]', 'active', TRUE, 'Wireless Earbuds Pro', 'Premium wireless earbuds with noise cancellation');

-- Demo shipping zones
INSERT INTO shipping_zones (name, description, countries, status) VALUES
('Domestic', 'United States domestic shipping', '["US"]', 'active'),
('North America', 'Canada and Mexico', '["CA", "MX"]', 'active'),
('Europe', 'European Union countries', '["DE", "FR", "IT", "ES", "NL", "BE", "AT", "PT", "IE", "FI", "SE", "DK", "PL", "CZ", "HU", "RO", "BG", "HR", "SI", "SK", "LT", "LV", "EE", "CY", "MT", "LU"]', 'active'),
('Asia Pacific', 'Major Asia Pacific countries', '["JP", "AU", "NZ", "SG", "HK", "KR", "TW"]', 'active'),
('Rest of World', 'All other countries', '["*"]', 'active');

-- Demo shipping methods
INSERT INTO shipping_methods (zone_id, name, description, base_rate, per_kg_rate, free_shipping_threshold, estimated_delivery_days, status) VALUES
(1, 'Standard Shipping', 'Standard domestic delivery', 5.99, 1.50, 50.00, '3-5 business days', 'active'),
(1, 'Express Shipping', 'Fast domestic delivery', 12.99, 2.00, 100.00, '1-2 business days', 'active'),
(1, 'Overnight Shipping', 'Next day delivery', 24.99, 3.00, NULL, '1 business day', 'active'),
(2, 'International Standard', 'Standard international shipping', 15.99, 3.00, 100.00, '7-14 business days', 'active'),
(2, 'International Express', 'Fast international shipping', 29.99, 4.00, 200.00, '3-7 business days', 'active'),
(3, 'EU Standard', 'Standard European shipping', 12.99, 2.50, 75.00, '5-10 business days', 'active'),
(3, 'EU Express', 'Fast European shipping', 24.99, 3.50, 150.00, '2-5 business days', 'active'),
(4, 'Asia Pacific Standard', 'Standard Asia Pacific shipping', 18.99, 3.50, 120.00, '10-21 business days', 'active'),
(4, 'Asia Pacific Express', 'Fast Asia Pacific shipping', 35.99, 5.00, 250.00, '5-10 business days', 'active'),
(5, 'Global Standard', 'Standard worldwide shipping', 22.99, 4.00, 150.00, '14-28 business days', 'active');

-- Demo customer addresses
INSERT INTO customer_addresses (customer_id, type, first_name, last_name, company, address_line_1, address_line_2, city, state, postal_code, country, phone, is_default) VALUES
(3, 'shipping', 'Jane', 'Customer', NULL, '123 Main Street', 'Apt 4B', 'New York', 'NY', '10001', 'US', '+1234567892', TRUE),
(3, 'billing', 'Jane', 'Customer', NULL, '123 Main Street', 'Apt 4B', 'New York', 'NY', '10001', 'US', '+1234567892', TRUE),
(5, 'shipping', 'Sarah', 'Wilson', 'Wilson Enterprises', '456 Oak Avenue', 'Suite 200', 'Los Angeles', 'CA', '90210', 'US', '+1234567894', TRUE),
(5, 'billing', 'Sarah', 'Wilson', 'Wilson Enterprises', '456 Oak Avenue', 'Suite 200', 'Los Angeles', 'CA', '90210', 'US', '+1234567894', TRUE);

-- Demo orders
INSERT INTO orders (customer_id, order_number, status, subtotal, tax_amount, shipping_amount, discount_amount, total, currency, payment_status, payment_method, billing_address, shipping_address, notes) VALUES
(3, 'ORD-20240115-ABC123', 'delivered', 1099.99, 87.99, 12.99, 0.00, 1200.97, 'USD', 'paid', 'credit_card',
'{"first_name":"Jane","last_name":"Customer","address_line_1":"123 Main Street","address_line_2":"Apt 4B","city":"New York","state":"NY","postal_code":"10001","country":"US","phone":"+1234567892"}',
'{"first_name":"Jane","last_name":"Customer","address_line_1":"123 Main Street","address_line_2":"Apt 4B","city":"New York","state":"NY","postal_code":"10001","country":"US","phone":"+1234567892"}',
'Please handle with care'),
(5, 'ORD-20240118-DEF456', 'shipped', 179.98, 14.40, 5.99, 20.00, 180.37, 'USD', 'paid', 'paypal',
'{"first_name":"Sarah","last_name":"Wilson","company":"Wilson Enterprises","address_line_1":"456 Oak Avenue","address_line_2":"Suite 200","city":"Los Angeles","state":"CA","postal_code":"90210","country":"US","phone":"+1234567894"}',
'{"first_name":"Sarah","last_name":"Wilson","company":"Wilson Enterprises","address_line_1":"456 Oak Avenue","address_line_2":"Suite 200","city":"Los Angeles","state":"CA","postal_code":"90210","country":"US","phone":"+1234567894"}',
NULL),
(3, 'ORD-20240120-GHI789', 'processing', 69.99, 5.60, 5.99, 0.00, 81.58, 'USD', 'paid', 'credit_card',
'{"first_name":"Jane","last_name":"Customer","address_line_1":"123 Main Street","address_line_2":"Apt 4B","city":"New York","state":"NY","postal_code":"10001","country":"US","phone":"+1234567892"}',
'{"first_name":"Jane","last_name":"Customer","address_line_1":"123 Main Street","address_line_2":"Apt 4B","city":"New York","state":"NY","postal_code":"10001","country":"US","phone":"+1234567892"}',
'Gift wrapping requested');

-- Demo order items
INSERT INTO order_items (order_id, product_id, seller_id, product_name, product_sku, quantity, price, total) VALUES
(1, 1, 2, 'iPhone 15 Pro Max', 'IPH15PM-256-TIT', 1, 1099.99, 1099.99),
(2, 4, 4, 'Designer Dress', 'DRESS-EVE-M-BLU', 1, 129.99, 129.99),
(2, 6, 4, 'Yoga Mat Premium', 'YOGA-MAT-PREM-PUR', 1, 39.99, 39.99),
(2, 8, 4, 'Wireless Earbuds Pro', 'EARBUDS-PRO-WHT', 1, 179.99, 179.99),
(3, 7, 4, 'Programming Book Set', 'BOOK-PROG-SET-3', 1, 69.99, 69.99);

-- Demo transactions
INSERT INTO transactions (order_id, seller_id, transaction_type, gross_amount, commission_rate, commission_amount, net_amount, status, payment_gateway, gateway_transaction_id, processed_at) VALUES
(1, 2, 'sale', 1099.99, 6.00, 65.99, 1033.00, 'completed', 'stripe', 'pi_**********', '2024-01-15 14:30:00'),
(2, 4, 'sale', 129.99, 8.00, 10.40, 119.59, 'completed', 'paypal', 'PAYID-ABCDEF', '2024-01-18 16:45:00'),
(2, 4, 'sale', 39.99, 8.00, 3.20, 36.79, 'completed', 'paypal', 'PAYID-ABCDEF', '2024-01-18 16:45:00'),
(2, 4, 'sale', 179.99, 8.00, 14.40, 165.59, 'completed', 'paypal', 'PAYID-ABCDEF', '2024-01-18 16:45:00'),
(3, 4, 'sale', 69.99, 8.00, 5.60, 64.39, 'pending', 'stripe', 'pi_**********', NULL);

-- Demo payouts
INSERT INTO payouts (seller_id, amount, status, request_date, processed_date, processed_by, payment_method, payment_details, notes) VALUES
(2, 1033.00, 'completed', '2024-01-16 10:00:00', '2024-01-17 14:30:00', 1, 'bank_transfer', '{"bank_name":"Chase Bank","account_number":"****1234","routing_number":"*********"}', 'Regular payout'),
(4, 321.97, 'pending', '2024-01-20 09:15:00', NULL, NULL, 'paypal', '{"paypal_email":"<EMAIL>"}', 'Requested payout'),
(4, 150.00, 'processing', '2024-01-18 15:30:00', NULL, 1, 'bank_transfer', '{"bank_name":"Bank of America","account_number":"****5678","routing_number":"*********"}', 'Processing payout');

-- Demo shipments
INSERT INTO shipments (order_id, tracking_number, carrier, shipping_method_id, status, shipped_date, estimated_delivery_date, delivered_date, delivery_notes) VALUES
(1, 'TRK-**********', 'UPS', 2, 'delivered', '2024-01-15 18:00:00', '2024-01-17', '2024-01-16 14:30:00', 'Delivered to front door'),
(2, 'TRK-**********', 'FedEx', 1, 'in_transit', '2024-01-19 10:30:00', '2024-01-24', NULL, NULL),
(3, NULL, NULL, NULL, 'pending', NULL, NULL, NULL, NULL);

-- Demo cart items
INSERT INTO cart_items (customer_id, product_id, quantity) VALUES
(3, 2, 1),
(3, 5, 1),
(5, 3, 2),
(5, 6, 1);

-- Demo system settings
INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', 'E-Commerce Platform', 'string', 'Website name'),
('site_description', 'Professional E-Commerce Platform', 'string', 'Website description'),
('default_currency', 'USD', 'string', 'Default currency code'),
('tax_rate', '8.0', 'number', 'Default tax rate percentage'),
('free_shipping_threshold', '50.00', 'number', 'Minimum order amount for free shipping'),
('max_file_upload_size', '5242880', 'number', 'Maximum file upload size in bytes (5MB)'),
('allowed_image_types', '["jpg","jpeg","png","gif","webp"]', 'json', 'Allowed image file types'),
('email_notifications', 'true', 'boolean', 'Enable email notifications'),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode'),
('featured_products_limit', '8', 'number', 'Number of featured products to display on homepage');
