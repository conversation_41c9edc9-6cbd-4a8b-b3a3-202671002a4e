/**
 * E-Commerce Platform Styles
 * Clean, responsive design without frameworks
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
}

a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #0056b3;
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.main-content {
    min-height: calc(100vh - 140px);
    padding-top: 80px;
}

/* Grid System */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.5rem;
}

.col {
    flex: 1;
    padding: 0 0.5rem;
}

.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
    line-height: 1.5;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: #fff;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}

.btn-secondary:hover {
    background-color: #545b62;
    border-color: #545b62;
    color: #fff;
}

.btn-outline {
    background-color: transparent;
    border-color: #007bff;
    color: #007bff;
}

.btn-outline:hover {
    background-color: #007bff;
    color: #fff;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: #fff;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff;
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

.btn-full {
    width: 100%;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: flex;
    gap: 1rem;
}

.form-row .form-group {
    flex: 1;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="number"],
input[type="url"],
input[type="date"],
textarea,
select {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    border: 1px solid #ddd;
    border-radius: 0.375rem;
    background-color: #fff;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
}

/* Navigation */
.navbar {
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-brand a {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    text-decoration: none;
}

.logo {
    height: 40px;
    margin-right: 0.5rem;
}

.logo-text {
    color: #007bff;
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-item {
    position: relative;
    margin-left: 1.5rem;
}

.nav-link {
    color: #333;
    font-weight: 500;
    padding: 0.5rem 0;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #007bff;
}

.dropdown {
    position: relative;
}

.dropdown-toggle::after {
    content: ' ▼';
    font-size: 0.8rem;
    margin-left: 0.5rem;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    list-style: none;
}

.dropdown-menu a {
    display: block;
    padding: 0.75rem 1rem;
    color: #333;
    border-bottom: 1px solid #f8f9fa;
}

.dropdown-menu a:hover {
    background-color: #f8f9fa;
    color: #007bff;
}

.cart-count {
    background-color: #dc3545;
    color: #fff;
    border-radius: 50%;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Alerts */
.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    position: relative;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-close {
    position: absolute;
    top: 0.5rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
}

.alert-close:hover {
    opacity: 1;
}

/* Cards */
.card {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.card-header {
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    font-weight: 600;
}

.card-body {
    padding: 1rem;
}

.card-footer {
    padding: 1rem;
    background-color: #f8f9fa;
    border-top: 1px solid #ddd;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
    background-color: #fff;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.table-striped tbody tr:nth-child(odd) {
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: #f5f5f5;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-block { display: inline-block; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 4rem 0;
    margin-top: -80px;
    padding-top: 8rem;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    gap: 3rem;
}

.hero-content {
    flex: 1;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
}

/* Sections */
.section-title {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.features-section,
.categories-section,
.featured-products-section,
.cta-section {
    padding: 4rem 0;
}

.features-section {
    background-color: #fff;
}

.categories-section {
    background-color: #f8f9fa;
}

.featured-products-section {
    background-color: #fff;
}

.cta-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    margin-bottom: 0;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.category-card {
    background-color: #fff;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.category-card:hover {
    transform: translateY(-3px);
}

.category-card a {
    display: block;
    text-decoration: none;
    color: inherit;
}

.category-card img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.category-placeholder {
    width: 100%;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    font-size: 3rem;
    color: #ccc;
}

.category-card h3 {
    padding: 1rem 1rem 0.5rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.category-card p {
    padding: 0 1rem 1rem;
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.product-card {
    background-color: #fff;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-3px);
}

.product-card a {
    display: block;
    text-decoration: none;
    color: inherit;
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-placeholder {
    width: 100%;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    font-size: 3rem;
    color: #ccc;
}

.product-info {
    padding: 1rem;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
    line-height: 1.3;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #007bff;
}

.sale-price {
    color: #dc3545;
    margin-right: 0.5rem;
}

.original-price {
    color: #999;
    text-decoration: line-through;
    font-size: 1rem;
    font-weight: normal;
}

.product-seller {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0;
}

/* CTA Section */
.cta-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.cta-feature {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.cta-icon {
    color: #28a745;
    font-weight: bold;
}

/* Authentication Pages */
.auth-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
}

.auth-container {
    width: 100%;
    max-width: 500px;
}

.auth-card {
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    overflow: hidden;
}

.auth-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
    background-color: #f8f9fa;
}

.auth-header h1 {
    color: #333;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: #666;
    margin-bottom: 0;
}

.auth-form {
    padding: 2rem;
}

.auth-footer {
    text-align: center;
    padding: 1rem 2rem 2rem;
    color: #666;
}

.forgot-link {
    font-size: 0.9rem;
    color: #007bff;
}

.demo-accounts {
    padding: 1rem 2rem 2rem;
    background-color: #f8f9fa;
    border-top: 1px solid #ddd;
}

.demo-accounts h3 {
    text-align: center;
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.1rem;
}

.demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.demo-account {
    text-align: center;
    padding: 1rem;
    background-color: #fff;
    border-radius: 0.375rem;
    border: 1px solid #ddd;
}

.demo-account h4 {
    margin-bottom: 0.5rem;
    color: #333;
    font-size: 1rem;
}

.demo-account p {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.25rem;
}

/* Dashboard Styles */
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    color: #666;
    font-size: 1.1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: #fff;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.stat-card.success {
    border-left-color: #28a745;
}

.stat-card.warning {
    border-left-color: #ffc107;
}

.stat-card.danger {
    border-left-color: #dc3545;
}

.stat-card.info {
    border-left-color: #17a2b8;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-change {
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

.stat-change.positive {
    color: #28a745;
}

.stat-change.negative {
    color: #dc3545;
}

/* Data Tables */
.data-table-container {
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.data-table-header {
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.data-table-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.data-table-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.search-box {
    position: relative;
}

.search-box input {
    padding-left: 2.5rem;
    width: 250px;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.filter-select {
    min-width: 150px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background-color: #f8f9fa;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #ddd;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 1rem;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.processing {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-badge.completed {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    padding: 0.5rem;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 0.25rem;
    transition: background-color 0.3s ease;
}

.btn-icon:hover {
    background-color: #f8f9fa;
}

.btn-icon.edit {
    color: #007bff;
}

.btn-icon.delete {
    color: #dc3545;
}

.btn-icon.view {
    color: #28a745;
}

/* Footer */
.footer {
    background-color: #333;
    color: #fff;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #fff;
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #007bff;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.social-link:hover {
    color: #007bff;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 1rem;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-bottom p {
    color: #ccc;
    margin: 0;
}

.footer-bottom-links {
    display: flex;
    gap: 1rem;
}

.footer-bottom-links a {
    color: #ccc;
    text-decoration: none;
    font-size: 0.9rem;
}

.footer-bottom-links a:hover {
    color: #007bff;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin: 2rem 0;
}

.pagination a,
.pagination span {
    padding: 0.5rem 0.75rem;
    border: 1px solid #ddd;
    color: #007bff;
    text-decoration: none;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

.pagination .current {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

.pagination .disabled {
    color: #999;
    cursor: not-allowed;
}

.pagination .disabled:hover {
    background-color: transparent;
    color: #999;
    border-color: #ddd;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 2000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: #fff;
    border-radius: 0.5rem;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 1rem;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    margin: 0;
    font-size: 1.2rem;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 1rem;
}

.modal-footer {
    padding: 1rem;
    border-top: 1px solid #ddd;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.loading-overlay .loading {
    width: 50px;
    height: 50px;
    border-width: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: #fff;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        transition: left 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-list {
        flex-direction: column;
        width: 100%;
        padding: 2rem 0;
    }

    .nav-item {
        margin: 0;
        width: 100%;
        text-align: center;
    }

    .nav-link {
        display: block;
        padding: 1rem;
        border-bottom: 1px solid #f8f9fa;
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background-color: #f8f9fa;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    /* Hero Section */
    .hero-container {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-actions {
        justify-content: center;
    }

    /* Grids */
    .features-grid {
        grid-template-columns: 1fr;
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .cta-features {
        grid-template-columns: 1fr;
    }

    /* Forms */
    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .auth-container {
        padding: 1rem;
    }

    .auth-form {
        padding: 1rem;
    }

    .demo-grid {
        grid-template-columns: 1fr;
    }

    /* Tables */
    .data-table-container {
        overflow-x: auto;
    }

    .data-table {
        min-width: 600px;
    }

    .data-table-header {
        flex-direction: column;
        align-items: stretch;
    }

    .data-table-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .search-box input {
        width: 100%;
    }

    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-bottom-links {
        justify-content: center;
    }

    /* Modal */
    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    /* Utility classes for mobile */
    .d-md-none {
        display: none;
    }

    .d-md-block {
        display: block;
    }
}

@media (max-width: 480px) {
    /* Typography */
    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.2rem; }

    .section-title {
        font-size: 1.8rem;
    }

    /* Hero */
    .hero-title {
        font-size: 1.8rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    /* Buttons */
    .btn-large {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    /* Cards */
    .feature-card,
    .category-card,
    .product-card {
        margin-bottom: 1rem;
    }

    /* Grids */
    .categories-grid,
    .products-grid {
        grid-template-columns: 1fr;
    }

    /* Dashboard */
    .dashboard-container {
        padding: 1rem;
    }

    .dashboard-title {
        font-size: 1.5rem;
    }

    /* Tables */
    .data-table th,
    .data-table td {
        padding: 0.5rem;
        font-size: 0.9rem;
    }

    /* Action buttons */
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .footer,
    .btn,
    .alert,
    .modal {
        display: none !important;
    }

    .main-content {
        padding-top: 0;
    }

    body {
        background-color: #fff;
        color: #000;
    }

    .card,
    .data-table-container {
        box-shadow: none;
        border: 1px solid #000;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    /* This can be implemented later for dark mode support */
}
