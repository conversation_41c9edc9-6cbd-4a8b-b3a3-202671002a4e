<?php
/**
 * Admin Dashboard
 * E-Commerce Platform
 */

$pageTitle = 'Admin Dashboard';
$bodyClass = 'admin-dashboard';

// Require admin access
require_once '../config/config.php';
requireRole('admin');

include '../includes/header.php';

// Get dashboard statistics
$stats = getDashboardStats('admin');

// Get recent orders
$recentOrders = getOrders([], 5);

// Get recent users
$recentUsers = $database->fetchAll(
    "SELECT * FROM users ORDER BY created_at DESC LIMIT 5"
);

// Get low stock products
$lowStockProducts = $database->fetchAll(
    "SELECT p.*, u.username as seller_name 
     FROM products p 
     LEFT JOIN users u ON p.seller_id = u.id 
     WHERE p.stock_quantity <= 10 AND p.status = 'active' 
     ORDER BY p.stock_quantity ASC 
     LIMIT 10"
);

// Get pending payouts
$pendingPayouts = $database->fetchAll(
    "SELECT p.*, u.username, u.first_name, u.last_name 
     FROM payouts p 
     LEFT JOIN users u ON p.seller_id = u.id 
     WHERE p.status = 'pending' 
     ORDER BY p.request_date ASC 
     LIMIT 5"
);
?>

<div class="dashboard-container">
    <div class="dashboard-header">
        <h1 class="dashboard-title">Admin Dashboard</h1>
        <p class="dashboard-subtitle">Welcome back, <?php echo htmlspecialchars($currentUser['first_name']); ?>! Here's what's happening with your platform.</p>
    </div>
    
    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value"><?php echo number_format($stats['total_users']); ?></div>
            <div class="stat-label">Total Users</div>
        </div>
        
        <div class="stat-card success">
            <div class="stat-value"><?php echo number_format($stats['total_products']); ?></div>
            <div class="stat-label">Total Products</div>
        </div>
        
        <div class="stat-card warning">
            <div class="stat-value"><?php echo number_format($stats['total_orders']); ?></div>
            <div class="stat-label">Total Orders</div>
        </div>
        
        <div class="stat-card info">
            <div class="stat-value"><?php echo formatCurrency($stats['total_revenue']); ?></div>
            <div class="stat-label">Total Revenue</div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3>Quick Actions</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-3">
                            <a href="users.php" class="btn btn-primary btn-full">Manage Users</a>
                        </div>
                        <div class="col-3">
                            <a href="products.php" class="btn btn-success btn-full">Manage Products</a>
                        </div>
                        <div class="col-3">
                            <a href="orders.php" class="btn btn-warning btn-full">View Orders</a>
                        </div>
                        <div class="col-3">
                            <a href="reports.php" class="btn btn-info btn-full">Generate Reports</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Recent Orders -->
        <div class="col-6">
            <div class="data-table-container">
                <div class="data-table-header">
                    <h3 class="data-table-title">Recent Orders</h3>
                    <a href="orders.php" class="btn btn-outline btn-small">View All</a>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($recentOrders)): ?>
                        <tr>
                            <td colspan="5" class="text-center">No orders found</td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($recentOrders as $order): ?>
                        <tr>
                            <td>
                                <a href="orders.php?id=<?php echo $order['id']; ?>">
                                    <?php echo htmlspecialchars($order['order_number']); ?>
                                </a>
                            </td>
                            <td><?php echo htmlspecialchars($order['first_name'] . ' ' . $order['last_name']); ?></td>
                            <td><?php echo formatCurrency($order['total']); ?></td>
                            <td>
                                <span class="status-badge <?php echo $order['status']; ?>">
                                    <?php echo ucfirst($order['status']); ?>
                                </span>
                            </td>
                            <td><?php echo formatDate($order['created_at'], 'M j, Y'); ?></td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Recent Users -->
        <div class="col-6">
            <div class="data-table-container">
                <div class="data-table-header">
                    <h3 class="data-table-title">Recent Users</h3>
                    <a href="users.php" class="btn btn-outline btn-small">View All</a>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Joined</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($recentUsers)): ?>
                        <tr>
                            <td colspan="5" class="text-center">No users found</td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($recentUsers as $user): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></td>
                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                            <td>
                                <span class="status-badge <?php echo $user['role']; ?>">
                                    <?php echo ucfirst($user['role']); ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $user['status']; ?>">
                                    <?php echo ucfirst($user['status']); ?>
                                </span>
                            </td>
                            <td><?php echo formatDate($user['created_at'], 'M j, Y'); ?></td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <!-- Low Stock Products -->
        <div class="col-6">
            <div class="data-table-container">
                <div class="data-table-header">
                    <h3 class="data-table-title">Low Stock Alert</h3>
                    <a href="products.php?filter=low_stock" class="btn btn-outline btn-small">View All</a>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Seller</th>
                            <th>Stock</th>
                            <th>Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($lowStockProducts)): ?>
                        <tr>
                            <td colspan="4" class="text-center">No low stock products</td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($lowStockProducts as $product): ?>
                        <tr>
                            <td>
                                <a href="products.php?id=<?php echo $product['id']; ?>">
                                    <?php echo htmlspecialchars($product['name']); ?>
                                </a>
                            </td>
                            <td><?php echo htmlspecialchars($product['seller_name']); ?></td>
                            <td>
                                <span class="status-badge <?php echo $product['stock_quantity'] <= 5 ? 'danger' : 'warning'; ?>">
                                    <?php echo $product['stock_quantity']; ?>
                                </span>
                            </td>
                            <td><?php echo formatCurrency($product['price']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Pending Payouts -->
        <div class="col-6">
            <div class="data-table-container">
                <div class="data-table-header">
                    <h3 class="data-table-title">Pending Payouts</h3>
                    <a href="payouts.php" class="btn btn-outline btn-small">View All</a>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Seller</th>
                            <th>Amount</th>
                            <th>Method</th>
                            <th>Requested</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($pendingPayouts)): ?>
                        <tr>
                            <td colspan="5" class="text-center">No pending payouts</td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($pendingPayouts as $payout): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($payout['first_name'] . ' ' . $payout['last_name']); ?></td>
                            <td><?php echo formatCurrency($payout['amount']); ?></td>
                            <td><?php echo ucfirst(str_replace('_', ' ', $payout['payment_method'])); ?></td>
                            <td><?php echo formatDate($payout['request_date'], 'M j, Y'); ?></td>
                            <td>
                                <div class="action-buttons">
                                    <a href="payouts.php?action=approve&id=<?php echo $payout['id']; ?>" 
                                       class="btn btn-success btn-small"
                                       onclick="return confirm('Approve this payout?')">
                                        Approve
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
