<?php
/**
 * Cart Count API
 * Returns the number of items in the user's cart
 */

header('Content-Type: application/json');
require_once '../config/config.php';

// Check if user is logged in and is a customer
if (!isLoggedIn() || !isCustomer()) {
    echo json_encode(['count' => 0]);
    exit;
}

try {
    $customerId = $_SESSION['user_id'];
    
    $result = $database->fetch(
        "SELECT SUM(quantity) as count FROM cart_items WHERE customer_id = ?",
        [$customerId]
    );
    
    $count = $result['count'] ?? 0;
    
    echo json_encode([
        'success' => true,
        'count' => (int)$count
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'count' => 0,
        'error' => 'Failed to get cart count'
    ]);
}
?>
