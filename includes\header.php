<?php
/**
 * Header Template
 * E-Commerce Platform
 */

// Include configuration
require_once __DIR__ . '/../config/config.php';

$currentUser = getCurrentUser();
$alert = getAlert();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME; ?></title>
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : 'Professional E-Commerce Platform'; ?>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo APP_URL; ?>/assets/css/style.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo APP_URL; ?>/assets/images/favicon.ico">
    
    <!-- Additional head content -->
    <?php if (isset($additionalHead)) echo $additionalHead; ?>
</head>
<body class="<?php echo isset($bodyClass) ? $bodyClass : ''; ?>">
    
    <!-- Alert Messages -->
    <?php if ($alert): ?>
    <div class="alert alert-<?php echo $alert['type']; ?>" id="alertMessage">
        <span class="alert-message"><?php echo htmlspecialchars($alert['message']); ?></span>
        <button type="button" class="alert-close" onclick="closeAlert()">&times;</button>
    </div>
    <?php endif; ?>
    
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="<?php echo APP_URL; ?>/index.php">
                    <img src="<?php echo APP_URL; ?>/assets/images/logo.png" alt="<?php echo APP_NAME; ?>" class="logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                    <span class="logo-text"><?php echo APP_NAME; ?></span>
                </a>
            </div>
            
            <div class="nav-menu" id="navMenu">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="<?php echo APP_URL; ?>/index.php" class="nav-link">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="<?php echo APP_URL; ?>/customer/index.php" class="nav-link">Shop</a>
                    </li>
                    
                    <?php if ($currentUser): ?>
                        <?php if ($currentUser['role'] === 'admin'): ?>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle">Admin</a>
                                <ul class="dropdown-menu">
                                    <li><a href="<?php echo APP_URL; ?>/admin/index.php">Dashboard</a></li>
                                    <li><a href="<?php echo APP_URL; ?>/admin/users.php">Users</a></li>
                                    <li><a href="<?php echo APP_URL; ?>/admin/products.php">Products</a></li>
                                    <li><a href="<?php echo APP_URL; ?>/admin/orders.php">Orders</a></li>
                                    <li><a href="<?php echo APP_URL; ?>/admin/payouts.php">Payouts</a></li>
                                    <li><a href="<?php echo APP_URL; ?>/admin/reports.php">Reports</a></li>
                                </ul>
                            </li>
                        <?php elseif ($currentUser['role'] === 'seller'): ?>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle">Seller</a>
                                <ul class="dropdown-menu">
                                    <li><a href="<?php echo APP_URL; ?>/seller/index.php">Dashboard</a></li>
                                    <li><a href="<?php echo APP_URL; ?>/seller/products.php">My Products</a></li>
                                    <li><a href="<?php echo APP_URL; ?>/seller/orders.php">Orders</a></li>
                                    <li><a href="<?php echo APP_URL; ?>/seller/payouts.php">Payouts</a></li>
                                    <li><a href="<?php echo APP_URL; ?>/seller/subscription.php">Subscription</a></li>
                                </ul>
                            </li>
                        <?php else: ?>
                            <li class="nav-item">
                                <a href="<?php echo APP_URL; ?>/customer/cart.php" class="nav-link">
                                    Cart <span class="cart-count" id="cartCount">0</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo APP_URL; ?>/customer/orders.php" class="nav-link">My Orders</a>
                            </li>
                        <?php endif; ?>
                        
                        <li class="nav-item dropdown">
                            <a href="#" class="nav-link dropdown-toggle">
                                <?php echo htmlspecialchars($currentUser['first_name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a href="<?php echo APP_URL; ?>/auth/profile.php">Profile</a></li>
                                <li><a href="<?php echo APP_URL; ?>/auth/logout.php">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a href="<?php echo APP_URL; ?>/auth/login.php" class="nav-link">Login</a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo APP_URL; ?>/auth/register.php" class="nav-link btn-primary">Register</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
            
            <div class="nav-toggle" id="navToggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
